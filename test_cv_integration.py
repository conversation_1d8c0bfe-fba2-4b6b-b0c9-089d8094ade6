#!/usr/bin/env python3
"""
Test script to verify CV integration works correctly
"""

import os
import sys
import numpy as np
from PIL import Image
import cv2

# Add current directory to path
sys.path.append('.')

try:
    from cv_model_app import (
        detect_roof_segmentation,
        detect_target_building_outline,
        estimate_roof_pitch_and_azimuth,
        tile_roof_with_panels,
        tile_roof_with_smart_placement,
        estimate_solar_heatmap,
        generate_roof_panel_layout,
        EnhancedSolarAnalyzerCV
    )
    print("✅ Successfully imported enhanced CV functions from cv_model_app")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def create_test_image():
    """Create a simple test satellite image"""
    # Create a 800x800 test image
    img = np.ones((800, 800, 3), dtype=np.uint8) * 100  # Gray background
    
    # Add a "roof" area (brighter rectangle in center)
    roof_area = img[200:600, 200:600]
    roof_area[:] = [220, 220, 220]  # Light gray roof
    
    # Save test image
    os.makedirs('temp', exist_ok=True)
    test_path = 'temp/test_satellite.jpg'
    cv2.imwrite(test_path, img)
    
    print(f"✅ Created test image: {test_path}")
    return test_path

def test_cv_functions():
    """Test individual CV functions"""
    print("\n🧪 Testing Enhanced CV Functions...")

    # Create test image
    test_image_path = create_test_image()

    try:
        # Test enhanced building detection
        print("1. Testing enhanced building detection...")
        test_lat, test_lng = 40.7128, -74.0060  # Test coordinates
        building_result = detect_target_building_outline(test_image_path, test_lat, test_lng, 20)
        if building_result['success']:
            print(f"   ✅ Building detection successful")
            print(f"   📍 Center coords: {building_result['center_coords']}")
            print(f"   🔍 Search radius: {building_result['search_radius']}")
            print(f"   📐 ROI bounds: {building_result['roi_bounds']}")
        else:
            print(f"   ⚠️ Building detection failed: {building_result.get('error', 'Unknown error')}")

        # Test enhanced roof segmentation
        print("2. Testing enhanced roof segmentation...")
        mask = detect_roof_segmentation(test_image_path, test_lat, test_lng, 20)
        print(f"   ✅ Enhanced roof mask shape: {mask.shape}")

        # Test elevation estimation
        print("3. Testing elevation estimation...")
        elevation_info = estimate_roof_pitch_and_azimuth(mask)
        print(f"   ✅ Elevation info: {elevation_info}")

        # Test enhanced panel tiling
        print("4. Testing enhanced panel tiling...")
        building_contour = building_result.get('building_contour') if building_result['success'] else None
        if building_contour is not None:
            panel_grid = tile_roof_with_smart_placement(mask, building_contour, elevation_info)
            print(f"   ✅ Smart placement found {len(panel_grid)} panel positions")
        else:
            panel_grid = tile_roof_with_panels(mask, elevation_info)
            print(f"   ✅ Standard tiling found {len(panel_grid)} panel positions")

        # Test heatmap generation
        print("5. Testing heatmap generation...")
        heatmap = estimate_solar_heatmap(panel_grid, elevation_info)
        print(f"   ✅ Generated heatmap with {len(heatmap)} values")

        # Test enhanced full pipeline
        print("6. Testing enhanced full CV pipeline...")
        result = generate_roof_panel_layout(test_image_path, test_lat, test_lng, 20)
        print(f"   ✅ Enhanced pipeline result keys: {list(result.keys())}")
        print(f"   ✅ Summary: {result['summary']}")
        if 'building_info' in result and result['building_info']:
            print(f"   🏠 Building info available: {result['building_info']['success']}")

        return True

    except Exception as e:
        print(f"   ❌ Enhanced CV function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analyzer_class():
    """Test the main analyzer class"""
    print("\n🧪 Testing EnhancedSolarAnalyzerCV Class...")
    
    try:
        # Initialize analyzer
        analyzer = EnhancedSolarAnalyzerCV()
        print("   ✅ Analyzer initialized successfully")
        
        # Test CSV functionality
        analyzer.save_to_csv("Test Address", 40.7128, -74.0060, "Test")
        print("   ✅ CSV save functionality works")
        
        # Test enhanced CV analysis method
        test_image_path = 'temp/test_satellite.jpg'
        if os.path.exists(test_image_path):
            # Test without coordinates (original method)
            cv_result = analyzer.get_cv_solar_analysis(test_image_path)
            if cv_result['success']:
                print("   ✅ CV analysis method (original) works")
                print(f"   📊 CV Data: {cv_result['cv_data']['summary']}")
            else:
                print(f"   ❌ CV analysis failed: {cv_result['error']}")
                return False

            # Test with coordinates (enhanced method)
            test_lat, test_lng = 40.7128, -74.0060
            cv_result_enhanced = analyzer.get_cv_solar_analysis(test_image_path, test_lat, test_lng, 20)
            if cv_result_enhanced['success']:
                print("   ✅ Enhanced CV analysis method works")
                print(f"   📊 Enhanced CV Data: {cv_result_enhanced['cv_data']['summary']}")
                if 'building_info' in cv_result_enhanced['cv_data']:
                    building_info = cv_result_enhanced['cv_data']['building_info']
                    if building_info and building_info['success']:
                        print("   🏠 Building targeting successful")
                    else:
                        print("   ⚠️ Building targeting not available")
            else:
                print(f"   ❌ Enhanced CV analysis failed: {cv_result_enhanced['error']}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Analyzer class test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting CV Integration Tests...")
    
    # Test CV functions
    cv_test_passed = test_cv_functions()
    
    # Test analyzer class
    analyzer_test_passed = test_analyzer_class()
    
    # Summary
    print("\n📊 Test Results Summary:")
    print(f"   CV Functions: {'✅ PASSED' if cv_test_passed else '❌ FAILED'}")
    print(f"   Analyzer Class: {'✅ PASSED' if analyzer_test_passed else '❌ FAILED'}")
    
    if cv_test_passed and analyzer_test_passed:
        print("\n🎉 All tests passed! CV integration is working correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
