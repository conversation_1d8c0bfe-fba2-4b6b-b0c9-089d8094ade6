#!/usr/bin/env python3
"""
Test script to verify CV integration works correctly
"""

import os
import sys
import numpy as np
from PIL import Image
import cv2

# Add current directory to path
sys.path.append('.')

try:
    from cv_model_app import (
        detect_roof_segmentation,
        estimate_roof_pitch_and_azimuth,
        tile_roof_with_panels,
        estimate_solar_heatmap,
        generate_roof_panel_layout,
        EnhancedSolarAnalyzerCV
    )
    print("✅ Successfully imported CV functions from cv_model_app")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def create_test_image():
    """Create a simple test satellite image"""
    # Create a 800x800 test image
    img = np.ones((800, 800, 3), dtype=np.uint8) * 100  # Gray background
    
    # Add a "roof" area (brighter rectangle in center)
    roof_area = img[200:600, 200:600]
    roof_area[:] = [220, 220, 220]  # Light gray roof
    
    # Save test image
    os.makedirs('temp', exist_ok=True)
    test_path = 'temp/test_satellite.jpg'
    cv2.imwrite(test_path, img)
    
    print(f"✅ Created test image: {test_path}")
    return test_path

def test_cv_functions():
    """Test individual CV functions"""
    print("\n🧪 Testing CV Functions...")
    
    # Create test image
    test_image_path = create_test_image()
    
    try:
        # Test roof segmentation
        print("1. Testing roof segmentation...")
        mask = detect_roof_segmentation(test_image_path)
        print(f"   ✅ Roof mask shape: {mask.shape}")
        
        # Test elevation estimation
        print("2. Testing elevation estimation...")
        elevation_info = estimate_roof_pitch_and_azimuth(mask)
        print(f"   ✅ Elevation info: {elevation_info}")
        
        # Test panel tiling
        print("3. Testing panel tiling...")
        panel_grid = tile_roof_with_panels(mask, elevation_info)
        print(f"   ✅ Found {len(panel_grid)} potential panel positions")
        
        # Test heatmap generation
        print("4. Testing heatmap generation...")
        heatmap = estimate_solar_heatmap(panel_grid, elevation_info)
        print(f"   ✅ Generated heatmap with {len(heatmap)} values")
        
        # Test full pipeline
        print("5. Testing full CV pipeline...")
        result = generate_roof_panel_layout(test_image_path)
        print(f"   ✅ Full pipeline result keys: {list(result.keys())}")
        print(f"   ✅ Summary: {result['summary']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ CV function test failed: {e}")
        return False

def test_analyzer_class():
    """Test the main analyzer class"""
    print("\n🧪 Testing EnhancedSolarAnalyzerCV Class...")
    
    try:
        # Initialize analyzer
        analyzer = EnhancedSolarAnalyzerCV()
        print("   ✅ Analyzer initialized successfully")
        
        # Test CSV functionality
        analyzer.save_to_csv("Test Address", 40.7128, -74.0060, "Test")
        print("   ✅ CSV save functionality works")
        
        # Test CV analysis method
        test_image_path = 'temp/test_satellite.jpg'
        if os.path.exists(test_image_path):
            cv_result = analyzer.get_cv_solar_analysis(test_image_path)
            if cv_result['success']:
                print("   ✅ CV analysis method works")
                print(f"   📊 CV Data: {cv_result['cv_data']['summary']}")
            else:
                print(f"   ❌ CV analysis failed: {cv_result['error']}")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Analyzer class test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting CV Integration Tests...")
    
    # Test CV functions
    cv_test_passed = test_cv_functions()
    
    # Test analyzer class
    analyzer_test_passed = test_analyzer_class()
    
    # Summary
    print("\n📊 Test Results Summary:")
    print(f"   CV Functions: {'✅ PASSED' if cv_test_passed else '❌ FAILED'}")
    print(f"   Analyzer Class: {'✅ PASSED' if analyzer_test_passed else '❌ FAILED'}")
    
    if cv_test_passed and analyzer_test_passed:
        print("\n🎉 All tests passed! CV integration is working correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
